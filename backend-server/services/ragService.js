const { GoogleGenerativeAI } = require('@google/generative-ai');
// 加载环境变量 (优先读取项目根目录或 backend-server/.env)
require('dotenv').config({ path: require('path').resolve(__dirname, '../.env') });
const fs = require('fs-extra');
const path = require('path');
const glob = require('glob');

// 简单的内存向量存储
class MemoryVectorStore {
  constructor() {
    this.documents = [];
    this.embeddings = [];
    this.metadatas = [];
  }

  async add(data) {
    const { ids, documents, embeddings, metadatas } = data;
    for (let i = 0; i < ids.length; i++) {
      this.documents.push({
        id: ids[i],
        content: documents[i],
        embedding: embeddings[i],
        metadata: metadatas[i]
      });
    }
  }

  async query(queryEmbedding, nResults = 5) {
    // 计算余弦相似度
    const similarities = this.documents.map(doc => {
      const similarity = this.cosineSimilarity(queryEmbedding, doc.embedding);
      return { ...doc, similarity };
    });

    // 按相似度排序并返回前 n 个结果
    const results = similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, nResults);

    return {
      documents: [results.map(r => r.content)],
      metadatas: [results.map(r => r.metadata)],
      distances: [results.map(r => 1 - r.similarity)] // 距离 = 1 - 相似度
    };
  }

  cosineSimilarity(a, b) {
    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
    const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
    const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
    return dotProduct / (magnitudeA * magnitudeB);
  }

  getCount() {
    return this.documents.length;
  }
}

/**
 * RAG 智能助手服务
 * 实现检索增强生成功能，为 Industrial Geo Dev 项目提供智能问答
 */
class RAGService {
  constructor() {
    this.chroma = null;
    this.collection = null;
    this.gemini = null;
    this.embeddingModel = null;
    this.isInitialized = false;
    this.knowledgeBaseBuilt = false;
    
    // 配置参数
    this.config = {
      collectionName: 'industrial_geo_knowledge',
      embeddingModel: 'text-embedding-004',
      generativeModel: 'gemini-1.5-flash', // 经济实惠的模型选择
      chunkSize: 1000,
      chunkOverlap: 200,
      maxRetrievalResults: 5
    };
  }

  /**
   * 初始化 RAG 服务
   */
  async initialize() {
    try {
      console.log('🚀 初始化 RAG 智能助手服务...');

      // 检查 Gemini API 密钥
      if (!process.env.GEMINI_API_KEY) {
        console.error('❌ 未设置 GEMINI_API_KEY，无法连接 Gemini API');
        throw new Error('GEMINI_API_KEY 未配置');
      }

      // 初始化内存向量存储
      this.vectorStore = new MemoryVectorStore();

      // 初始化 Gemini (用于嵌入和生成)，添加网络配置
      this.gemini = new GoogleGenerativeAI(process.env.GEMINI_API_KEY, {
        // 添加请求配置以改善网络连接
        timeout: 30000, // 30秒超时
        apiVersion: 'v1beta',
      });

      // 添加网络连接测试
      await this.testNetworkConnection();

      this.isInitialized = true;
      console.log('✅ RAG 服务初始化完成');

      // 自动构建知识库（如果尚未构建）
      if (!this.knowledgeBaseBuilt) {
        console.log('🔄 检测到知识库未构建，开始自动构建...');
        this.buildKnowledgeBase().catch(error => {
          console.error('❌ 自动构建知识库失败:', error);
        });
      }

    } catch (error) {
      console.error('❌ RAG 服务初始化失败:', error);
      this.isInitialized = false;
      // 不抛出错误，允许服务在降级模式下运行
    }
  }

  /**
   * 测试网络连接
   */
  async testNetworkConnection() {
    try {
      console.log('🔗 测试 Gemini API 网络连接...');
      
      // 简单的连接测试
      const testModel = this.gemini.getGenerativeModel({ 
        model: "gemini-1.5-flash" 
      });
      
      // 发送一个简单的测试请求
      const testPrompt = "Hello";
      const testResult = await Promise.race([
        testModel.generateContent(testPrompt),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('网络连接超时')), 10000)
        )
      ]);
      
      console.log('✅ Gemini API 网络连接正常');
      return true;
      
    } catch (error) {
      console.error('❌ Gemini API 网络连接测试失败:', error.message);
      
      // 提供详细的错误诊断
      if (error.message.includes('fetch failed')) {
        console.error(`
🚨 网络连接问题诊断:
1. 您的网络可能无法访问 Google AI 服务
2. 如果在中国境内，可能需要配置代理或VPN
3. 检查防火墙设置，确保允许访问 generativelanguage.googleapis.com
4. 尝试使用其他网络环境（如移动热点）

🔧 临时解决方案:
- 系统将使用本地文本匹配作为后备方案
- 可以继续使用基础功能，但AI回答质量会降低
        `);
      }
      
      return false;
    }
  }


  /**
   * 构建项目知识库
   */
  async buildKnowledgeBase() {
    try {
      console.log('🔨 开始构建项目知识库...');
      
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      // 收集项目文档和代码
      const documents = await this.collectProjectDocuments();
      
      // 检测网络连接状态，决定使用哪种构建方式
      const networkAvailable = await this.testEmbeddingConnection();
      
      if (networkAvailable) {
        console.log('🌐 使用在线嵌入向量构建知识库');
        await this.buildKnowledgeBaseWithEmbeddings(documents);
      } else {
        console.log('📶 网络不可用，使用离线文本匹配构建知识库');
        await this.buildKnowledgeBaseWithoutEmbeddings(documents);
      }
      
      console.log(`✅ 知识库构建完成，共处理 ${documents.length} 个文档`);
      this.knowledgeBaseBuilt = true; // 标记知识库已构建
      
    } catch (error) {
      console.error('❌ 构建知识库失败:', error);
      throw error;
    }
  }

  /**
   * 测试嵌入API连接
   */
  async testEmbeddingConnection() {
    try {
      if (!this.gemini) return false;
      
      const model = this.gemini.getGenerativeModel({ 
        model: "text-embedding-004" 
      });
      
      await Promise.race([
        model.embedContent("test"),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('超时')), 5000)
        )
      ]);
      
      return true;
    } catch (error) {
      console.warn('🔗 嵌入API连接测试失败，将使用离线模式');
      return false;
    }
  }

  /**
   * 使用嵌入向量构建知识库（在线模式）
   */
  async buildKnowledgeBaseWithEmbeddings(documents) {
    let processedCount = 0;
    for (const doc of documents) {
      await this.processAndStoreDocument(doc);
      processedCount++;
      
      if (processedCount % 10 === 0) {
        console.log(`📄 已处理 ${processedCount}/${documents.length} 个文档`);
      }
    }
  }

  /**
   * 不使用嵌入向量构建知识库（离线模式）
   */
  async buildKnowledgeBaseWithoutEmbeddings(documents) {
    let processedCount = 0;
    for (const doc of documents) {
      // 将文档切分成块
      const chunks = this.chunkDocument(doc);

      // 为每个块存储，使用简单哈希向量
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const chunkId = `${doc.path}_chunk_${i}`;

        // 直接使用简单哈希向量
        const embedding = this.createSimpleTextVector(chunk.content);

        // 存储到内存向量数据库
        await this.vectorStore.add({
          ids: [chunkId],
          documents: [chunk.content],
          embeddings: [embedding],
          metadatas: [{
            file_path: doc.path,
            file_type: doc.type,
            chunk_index: i,
            chunk_size: chunk.content.length,
            created_at: new Date().toISOString(),
            mode: 'offline' // 标记为离线模式
          }]
        });
      }

      processedCount++;
      if (processedCount % 10 === 0) {
        console.log(`📄 已处理 ${processedCount}/${documents.length} 个文档 (离线模式)`);
      }
    }
  }

  /**
   * 收集项目文档和代码文件
   */
  async collectProjectDocuments() {
    const projectRoot = path.resolve(__dirname, '../..');
    const documents = [];
    
    // 文档文件模式
    const documentPatterns = [
      '*.md',
      'src/**/*.jsx',
      'src/**/*.js',
      'src/**/*.css',
      'backend-server/**/*.js',
      'backend-server/**/*.cjs',
      'backend-server/**/*.py',
      'package.json',
      'backend-server/package.json'
    ];
    
    // 排除模式
    const excludePatterns = [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.git/**',
      '**/chroma_db/**'
    ];
    
    for (const pattern of documentPatterns) {
      const files = glob.sync(pattern, {
        cwd: projectRoot,
        ignore: excludePatterns,
        absolute: true
      });
      
      for (const filePath of files) {
        try {
          const content = await fs.readFile(filePath, 'utf-8');
          const relativePath = path.relative(projectRoot, filePath);
          
          documents.push({
            path: relativePath,
            fullPath: filePath,
            content: content,
            type: this.getDocumentType(filePath),
            size: content.length
          });
        } catch (error) {
          console.warn(`⚠️ 无法读取文件 ${filePath}:`, error.message);
        }
      }
    }
    
    console.log(`📁 收集到 ${documents.length} 个文档文件`);
    return documents;
  }

  /**
   * 获取文档类型
   */
  getDocumentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const fileName = path.basename(filePath).toLowerCase();
    
    if (ext === '.md') return 'documentation';
    if (ext === '.jsx' || ext === '.js') return 'frontend_code';
    if (ext === '.cjs') return 'backend_code';
    if (ext === '.py') return 'python_code';
    if (ext === '.css') return 'styles';
    if (fileName === 'package.json') return 'configuration';
    
    return 'other';
  }

  /**
   * 处理并存储单个文档
   */
  async processAndStoreDocument(document) {
    try {
      console.log(`[处理中] -> ${document.path}`);
      // 将文档切分成块
      const chunks = this.chunkDocument(document);

      // 为每个块生成嵌入向量并存储
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const chunkId = `${document.path}_chunk_${i}`;

        // 生成嵌入向量
        const embedding = await this.generateEmbedding(chunk.content);

        // 存储到内存向量数据库
        await this.vectorStore.add({
          ids: [chunkId],
          documents: [chunk.content],
          embeddings: [embedding],
          metadatas: [{
            file_path: document.path,
            file_type: document.type,
            chunk_index: i,
            chunk_size: chunk.content.length,
            created_at: new Date().toISOString()
          }]
        });
      }

    } catch (error) {
      console.error(`❌ 处理文档失败 ${document.path}:`, error);
    }
  }

  /**
   * 将文档切分成块
   */
  chunkDocument(document) {
    const content = document.content;
    const chunks = [];
    
    // 根据文档类型选择不同的切分策略
    if (document.type === 'documentation') {
      // Markdown 文档按章节切分
      return this.chunkMarkdown(content);
    } else if (document.type.includes('code')) {
      // 代码文件按函数/类切分
      return this.chunkCode(content, document.type);
    } else {
      // 其他文件按固定大小切分
      return this.chunkBySize(content);
    }
  }

  /**
   * 按章节切分 Markdown 文档
   */
  chunkMarkdown(content) {
    const chunks = [];
    const sections = content.split(/\n(?=#{1,6}\s)/);
    
    for (const section of sections) {
      if (section.trim().length > 0) {
        if (section.length <= this.config.chunkSize) {
          chunks.push({
            content: section.trim(),
            type: 'section'
          });
        } else {
          // 大章节进一步切分
          const subChunks = this.chunkBySize(section);
          chunks.push(...subChunks);
        }
      }
    }
    
    return chunks;
  }

  /**
   * 按函数/类切分代码
   */
  chunkCode(content, type) {
    const chunks = [];
    
    if (type === 'frontend_code' || type === 'backend_code') {
      // JavaScript/JSX 代码按函数切分
      const functionPattern = /(?:function\s+\w+|const\s+\w+\s*=|class\s+\w+|export\s+(?:default\s+)?(?:function|class|const))/g;
      const matches = [...content.matchAll(functionPattern)];
      
      if (matches.length > 0) {
        let lastIndex = 0;
        for (let i = 0; i < matches.length; i++) {
          const match = matches[i];
          const nextMatch = matches[i + 1];
          const endIndex = nextMatch ? nextMatch.index : content.length;
          
          const chunk = content.slice(lastIndex, endIndex).trim();
          if (chunk.length > 0) {
            chunks.push({
              content: chunk,
              type: 'function'
            });
          }
          lastIndex = match.index;
        }
      }
    }
    
    // 如果没有找到函数，按固定大小切分
    if (chunks.length === 0) {
      return this.chunkBySize(content);
    }
    
    return chunks;
  }

  /**
   * 按固定大小切分文档
   */
  chunkBySize(content) {
    const chunks = [];
    const lines = content.split('\n');
    let currentChunk = '';
    
    for (const line of lines) {
      if (currentChunk.length + line.length > this.config.chunkSize) {
        if (currentChunk.trim().length > 0) {
          chunks.push({
            content: currentChunk.trim(),
            type: 'text'
          });
        }
        currentChunk = line;
      } else {
        currentChunk += (currentChunk ? '\n' : '') + line;
      }
    }
    
    if (currentChunk.trim().length > 0) {
      chunks.push({
        content: currentChunk.trim(),
        type: 'text'
      });
    }
    
    return chunks;
  }

  /**
   * 生成文本嵌入向量
   */
  async generateEmbedding(text) {
    try {
      if (!this.gemini) {
        throw new Error('Gemini 客户端未初始化');
      }

      // 添加重试机制
      const maxRetries = 3;
      const retryDelay = 1000; // 1秒

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(`🔄 尝试生成嵌入向量 (第${attempt}次)`);
          
          // 使用正确的嵌入模型API，添加超时控制
          const model = this.gemini.getGenerativeModel({ 
            model: "text-embedding-004" 
          });
          
          const result = await Promise.race([
            model.embedContent(text),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('嵌入生成超时')), 30000) // 延长超时到30秒
            )
          ]);
          
          console.log(`✅ 嵌入向量生成成功 (第${attempt}次尝试)`);
          return result.embedding.values;

        } catch (retryError) {
          console.warn(`⚠️ 第${attempt}次尝试失败:`, retryError.message);
          
          if (attempt < maxRetries) {
            console.log(`🔄 等待${retryDelay}ms后重试...`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
          } else {
            throw retryError;
          }
        }
      }

    } catch (error) {
      console.error('❌ 生成嵌入向量失败:', error);
      
      // 如果嵌入生成失败，返回一个简单的文本哈希向量作为后备
      const simpleHash = this.createSimpleTextVector(text);
      console.log('📝 使用简单哈希向量作为后备');
      return simpleHash;
    }
  }

  /**
   * 创建简单的文本向量（作为嵌入API失败时的后备）
   */
  createSimpleTextVector(text) {
    // 创建一个简单的384维向量
    const vector = new Array(384).fill(0);
    
    // 基于文本内容生成简单的特征向量
    for (let i = 0; i < text.length && i < 384; i++) {
      vector[i % 384] += text.charCodeAt(i) / 1000;
    }
    
    // 标准化向量
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    return vector.map(val => magnitude > 0 ? val / magnitude : 0);
  }

  /**
   * 构建智能提示词
   */
  buildPrompt(question, relevantChunks) {
    const context = relevantChunks.map(chunk => {
      const fileInfo = chunk.metadata.file_path ? `[${chunk.metadata.file_path}]` : '[Unknown]';
      return `${fileInfo}\n${chunk.content}`;
    }).join('\n\n---\n\n');

    return `你是 Industrial Geo Dev 项目的专业AI助手，专门为用户提供关于地理信息系统、数据分析和工业选址的专业指导。

## 项目背景
Industrial Geo Dev 是一个先进的地理信息和数据分析平台，主要功能包括：
- 交互式地图分析和可视化
- 劳动力趋势和经济数据分析 (QCEW, LAUS数据)
- CPI成本上涨计算和预测
- 工业选址分析和评估
- 多层次的地理数据展示
- AI驱动的数据洞察

## 你的角色
作为嵌入在系统内部的项目专家，你需要：
1. 深度理解用户的问题和需求
2. 基于项目代码和文档提供准确、实用的答案
3. 引导用户正确使用系统功能
4. 提供清晰的操作步骤和技术建议
5. 当遇到复杂问题时，提供分步骤的解决方案

## 上下文信息
以下是与用户问题相关的项目代码和文档片段：

${context}

## 用户问题
${question}

## 回答要求
请基于上述上下文信息，为用户提供详细、准确且实用的回答。如果问题涉及：
- **功能使用**：提供具体的操作步骤
- **技术问题**：解释原理并给出解决方案
- **数据分析**：说明数据来源、计算方法和解读方式
- **代码相关**：引用具体的文件路径和代码片段
- **系统配置**：提供配置建议和注意事项

请用中文回答，语言要专业但易懂，确保用户能够轻松理解和执行你的建议。`;
  }

  /**
   * 生成流式回答
   */
  async generateAnswerStream(prompt, onChunk) {
    try {
      if (!this.gemini) {
        throw new Error('Gemini 客户端未初始化');
      }
      
      const model = this.gemini.getGenerativeModel({
        model: this.config.generativeModel,
        generationConfig: {
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 2048,
        }
      });
      
      // 使用 Gemini 的流式 API
      const result = await model.generateContentStream(prompt);
      
      let fullText = '';
      for await (const chunk of result.stream) {
        const chunkText = chunk.text();
        fullText += chunkText;
        
        // 调用回调函数发送流式数据
        if (onChunk) {
          onChunk({
            type: 'content',
            content: fullText,
            isComplete: false
          });
        }
      }
      
      // 发送完成信号
      if (onChunk) {
        onChunk({
          type: 'content',
          content: fullText,
          isComplete: true
        });
      }
      
      return fullText;
      
    } catch (error) {
      console.error('❌ 流式生成回答失败:', error);
      throw error;
    }
  }

  /**
   * 生成回答（非流式，保持兼容性）
   */
  async generateAnswer(prompt) {
    try {
      if (!this.gemini) {
        throw new Error('Gemini 客户端未初始化');
      }
      
      const model = this.gemini.getGenerativeModel({
        model: this.config.generativeModel,
        generationConfig: {
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 2048,
        }
      });
      
      const result = await model.generateContent(prompt);
      const response = await result.response;
      
      return response.text();
      
    } catch (error) {
      console.error('❌ 生成回答失败:', error);
      throw error;
    }
  }

  /**
   * 记录问答历史
   */
  async logQA(question, answer, userId) {
    try {
      // 这里可以将问答记录存储到数据库
      // 暂时只记录到控制台
      console.log(`📝 问答记录 - 用户: ${userId || 'anonymous'}, 问题: ${question.substring(0, 50)}...`);
    } catch (error) {
      console.error('❌ 记录问答历史失败:', error);
    }
  }

  /**
   * 检索相关知识片段 - 改进版
   */
  async retrieveRelevantChunks(question) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      if (this.vectorStore.getCount() === 0) {
        console.warn('⚠️ 知识库为空，返回默认响应');
        return [{
          content: '当前知识库正在构建中，请稍后重试或查看系统文档。',
          metadata: {
            file_path: 'system',
            file_type: 'default'
          }
        }];
      }
      
      // 生成问题的嵌入向量
      const queryEmbedding = await this.generateEmbedding(question);
      
      // 从向量存储中检索相关文档
      const results = await this.vectorStore.query(
        queryEmbedding, 
        this.config.maxRetrievalResults
      );
      
      const relevantChunks = [];
      for (let i = 0; i < results.documents[0].length; i++) {
        relevantChunks.push({
          content: results.documents[0][i],
          metadata: results.metadatas[0][i],
          distance: results.distances[0][i]
        });
      }
      
      console.log(`📚 检索到 ${relevantChunks.length} 个相关知识片段`);
      return relevantChunks;
      
    } catch (error) {
      console.error('❌ 检索相关知识片段失败:', error);
      
      // 返回默认片段，避免系统崩溃
      return [{
        content: '抱歉，知识检索暂时出现问题。请尝试重新表述您的问题或稍后再试。',
        metadata: {
          file_path: 'system_error',
          file_type: 'error'
        }
      }];
    }
  }

  /**
   * 获取系统状态
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      hasGeminiClient: !!this.gemini,
      hasCollection: this.vectorStore && this.vectorStore.getCount() > 0,
      documentCount: this.vectorStore ? this.vectorStore.getCount() : 0,
      knowledgeBaseBuilt: this.knowledgeBaseBuilt,
      model: this.config.generativeModel,
      embeddingModel: this.config.embeddingModel
    };
  }

  /**
   * 智能问答 - 增强版
   */
  async askQuestion(question, userId = null) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      console.log(`🤔 用户提问: ${question}`);
      
      // 1. 检索相关知识片段
      const relevantChunks = await this.retrieveRelevantChunks(question);
      
      // 2. 构建上下文提示
      const prompt = this.buildPrompt(question, relevantChunks);
      
      // 3. 调用 Gemini 生成回答
      const answer = await this.generateAnswer(prompt);
      
      // 4. 记录问答历史（可选）
      await this.logQA(question, answer, userId);
      
      return {
        question,
        answer,
        sources: relevantChunks.map(chunk => ({
          file_path: chunk.metadata.file_path,
          file_type: chunk.metadata.file_type
        })),
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('❌ 智能问答失败:', error);
      
      // 返回友好的错误消息
      return {
        question,
        answer: '抱歉，我暂时无法回答您的问题。可能的原因：\n1. AI服务暂时不可用\n2. 知识库正在更新\n3. 网络连接问题\n\n请稍后重试，或尝试重新表述您的问题。',
        sources: [],
        error: true,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 获取智能建议问题列表
   */
  getSuggestions() {
    return {
      success: true,
      data: [
        {
          category: '系统功能指南',
          questions: [
            '系统主要有哪些功能模块？',
            '如何使用交互式地图进行数据分析？',
            '如何使用站点选择分析工具？'
          ]
        },
        {
          category: '数据分析功能',
          questions: [
            '如何分析劳动力趋势？',
            '如何查看和分析QCEW工资数据？',
            'CPI成本上涨计算器如何使用？'
          ]
        },
        {
          category: '地图与可视化',
          questions: [
            '如何切换不同的地图图层？',
            '热力图表示什么含义？',
            '如何使用地图测量工具？'
          ]
        }
      ],
      timestamp: new Date().toISOString()
    };
  }
}

module.exports = RAGService;
