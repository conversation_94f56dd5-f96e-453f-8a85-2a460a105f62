const express = require('express');
const { body, validationResult } = require('express-validator');
const { optionalAuth } = require('../middleware/auth');

const router = express.Router();

/**
 * 🤖 智能问答端点
 * POST /api/rag/ask
 */
router.post('/ask', [
  optionalAuth, // 可选认证，支持匿名用户
  body('question')
    .notEmpty()
    .withMessage('问题不能为空')
    .isLength({ min: 3, max: 1000 })
    .withMessage('问题长度应在 3-1000 字符之间')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { question } = req.body;
    const userId = req.user?.id || null;
    const { ragService } = req; // 从请求中获取共享的 ragService 实例

    console.log(`🤖 RAG 问答请求 - 用户: ${userId || 'anonymous'}, 问题: ${question}`);

    // 调用 RAG 服务进行问答
    const result = await ragService.askQuestion(question, userId);

    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ RAG 问答失败:', error);
    
    // 根据错误类型返回不同的状态码
    let statusCode = 500;
    let message = '智能助手服务暂时不可用';
    
    if (error.message.includes('未初始化')) {
      statusCode = 503;
      message = '智能助手正在初始化，请稍后重试';
    } else if (error.message.includes('API')) {
      statusCode = 502;
      message = 'AI 服务连接失败，请稍后重试';
    }

    res.status(statusCode).json({
      success: false,
      message,
      error: process.env.NODE_ENV === 'development' ? error.message : undefined,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 🔄 流式问答端点
 * POST /api/rag/ask-stream
 */
router.post('/ask-stream', [
  optionalAuth,
  body('question')
    .notEmpty()
    .withMessage('问题不能为空')
    .isLength({ min: 3, max: 1000 })
    .withMessage('问题长度应在 3-1000 字符之间')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { question } = req.body;
    const userId = req.user?.id || null;
    const { ragService } = req; // 从请求中获取共享的 ragService 实例

    console.log(`🔄 RAG 流式问答请求 - 用户: ${userId || 'anonymous'}, 问题: ${question}`);

    // 设置 SSE 响应头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // 发送开始事件
    res.write(`data: ${JSON.stringify({ type: 'start', message: '正在思考...' })}\n\n`);

    try {
      // 检索相关知识片段
      res.write(`data: ${JSON.stringify({ type: 'status', message: '检索相关信息...' })}\n\n`);
      
      if (!ragService.isInitialized) {
        await ragService.initialize();
      }
      
      const relevantChunks = await ragService.retrieveRelevantChunks(question);
      
      // 发送检索结果
      res.write(`data: ${JSON.stringify({ 
        type: 'sources', 
        data: relevantChunks.map(chunk => ({
          file_path: chunk.metadata.file_path,
          file_type: chunk.metadata.file_type
        }))
      })}\n\n`);

      // 构建提示词
      const prompt = ragService.buildPrompt(question, relevantChunks);
      
      // 流式生成回答
      res.write(`data: ${JSON.stringify({ type: 'status', message: '生成回答...' })}\n\n`);
      
      let fullAnswer = '';
      
      // 使用真正的流式 API
      await ragService.generateAnswerStream(prompt, (data) => {
        fullAnswer = data.content;
        res.write(`data: ${JSON.stringify(data)}\n\n`);
      });

      // 发送完成事件
      res.write(`data: ${JSON.stringify({ 
        type: 'complete', 
        timestamp: new Date().toISOString() 
      })}\n\n`);

      // 记录问答历史
      await ragService.logQA(question, fullAnswer, userId);

    } catch (error) {
      console.error('❌ 流式问答处理失败:', error);
      res.write(`data: ${JSON.stringify({ 
        type: 'error', 
        message: '生成回答时出现错误，请稍后重试' 
      })}\n\n`);
    }

    res.end();

  } catch (error) {
    console.error('❌ 流式问答初始化失败:', error);
    res.status(500).json({
      success: false,
      message: '流式问答服务初始化失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 🔨 构建知识库端点
 * POST /api/rag/build-knowledge-base
 */
router.post('/build-knowledge-base', [
  optionalAuth // 暂时允许所有用户触发，生产环境应该限制权限
], async (req, res) => {
  try {
    console.log('🔨 开始构建知识库...');
    const { ragService } = req; // 从请求中获取共享的 ragService 实例
    
    // 异步构建知识库，立即返回响应
    ragService.buildKnowledgeBase().catch(error => {
      console.error('❌ 异步构建知识库失败:', error);
    });

    res.json({
      success: true,
      message: '知识库构建已开始，请稍后查看状态',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 启动知识库构建失败:', error);
    res.status(500).json({
      success: false,
      message: '启动知识库构建失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 📊 获取 RAG 服务状态
 * GET /api/rag/status
 */
router.get('/status', async (req, res) => {
  try {
    const { ragService } = req; // 从请求中获取共享的 ragService 实例
    const status = ragService.getStatus();
    
    // 检查知识库中的文档数量
    let documentCount = 0;
    if (status.hasCollection && ragService.collection) {
      try {
        const collectionInfo = await ragService.collection.count();
        documentCount = collectionInfo;
      } catch (error) {
        console.warn('⚠️ 无法获取文档数量:', error.message);
      }
    }

    res.json({
      success: true,
      status: {
        ...status,
        documentCount,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: {
          hasGeminiKey: !!process.env.GEMINI_API_KEY,
          chromaDbPath: process.env.CHROMA_DB_PATH || './chroma_db'
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 获取 RAG 状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取服务状态失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

/**
 * 🧪 RAG 服务健康检查
 * GET /api/rag/health
 */
router.get('/health', async (req, res) => {
  try {
    const { ragService } = req; // 从请求中获取共享的 ragService 实例
    const status = ragService.getStatus();
    
    const healthChecks = {
      ragService: false,
      geminiConnection: false,
      vectorStore: false
    };

    // 检查 RAG 服务状态
    healthChecks.ragService = ragService.isInitialized;

    // 检查向量存储状态
    if (ragService.vectorStore) {
      healthChecks.vectorStore = ragService.vectorStore.documents.length > 0;
    }

    // 检查 Gemini 连接
    if (ragService.gemini) {
      try {
        const model = ragService.gemini.getGenerativeModel({ model: 'gemini-2.5-flash-lite' });
        await model.generateContent('test');
        healthChecks.geminiConnection = true;
      } catch (error) {
        console.warn('⚠️ Gemini 连接检查失败:', error.message);
      }
    }

    // 检查 Chroma 连接
    if (ragService.collection) {
      try {
        await ragService.collection.count();
        healthChecks.chromaConnection = true;
      } catch (error) {
        console.warn('⚠️ Chroma 连接检查失败:', error.message);
      }
    }

    const isHealthy = Object.values(healthChecks).every(check => check);

    res.status(isHealthy ? 200 : 503).json({
      success: isHealthy,
      service: 'RAG 智能助手服务',
      status: isHealthy ? '健康' : '部分功能不可用',
      checks: healthChecks,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ RAG 健康检查失败:', error);
    res.status(500).json({
      success: false,
      service: 'RAG 智能助手服务',
      status: '服务异常',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 💡 获取建议问题端点
 * GET /api/rag/suggestions
 */
router.get('/suggestions', async (req, res) => {
  try {
    const { ragService } = req; // 从请求中获取共享的 ragService 实例
    const suggestions = ragService.getSuggestions();
    res.json(suggestions);
  } catch (error) {
    console.error('❌ 获取建议问题失败:', error);
    res.status(500).json({
      success: false,
      message: '获取建议问题失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
