#!/usr/bin/env node

/**
 * AI 助手初始化脚本
 * 用于测试 RAG 服务并构建知识库
 */

const RAGService = require('./services/ragService');

async function initializeAIAssistant() {
  console.log('🤖 开始初始化 Industrial Geo Dev AI 助手...\n');
  
  const ragService = new RAGService();
  
  try {
    // 1. 初始化服务
    console.log('1️⃣ 初始化 RAG 服务...');
    await ragService.initialize();
    console.log('✅ RAG 服务初始化完成\n');
    
    // 2. 检查状态
    console.log('2️⃣ 检查服务状态...');
    const status = ragService.getStatus();
    console.log('状态信息:', JSON.stringify(status, null, 2));
    console.log('');
    
    // 3. 构建知识库 (使用重试机制)
    console.log('3️⃣ 开始构建项目知识库...');
    await buildKnowledgeBaseWithRetry(ragService, 3);
    console.log('✅ 知识库构建完成\n');
    
    // 4. 最终状态检查
    console.log('4️⃣ 最终状态检查...');
    const finalStatus = ragService.getStatus();
    console.log('最终状态:', JSON.stringify(finalStatus, null, 2));
    
    // 5. 测试问答
    console.log('\n5️⃣ 测试智能问答功能...');
    await testQAFunctionality(ragService);
    
    console.log('\n🎉 AI 助手初始化完成！');
    console.log('💡 您现在可以启动服务器并使用 AI 助手功能了。');
    
  } catch (error) {
    console.error('❌ AI 助手初始化失败:', error.message);
    console.log('\n🔧 故障排除建议:');
    console.log('1. 检查网络连接');
    console.log('2. 验证 Gemini API 密钥是否有效');
    console.log('3. 确认 API 配额是否充足');
    console.log('4. 尝试重新运行初始化脚本');
    process.exit(1);
  }
}

/**
 * 带重试机制的知识库构建
 */
async function buildKnowledgeBaseWithRetry(ragService, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`📚 尝试构建知识库 (第 ${attempt}/${maxRetries} 次)...`);
      
      // 收集文档但限制数量以避免超时
      const documents = await ragService.collectProjectDocuments();
      console.log(`📁 收集到 ${documents.length} 个文档`);
      
      // 只处理前20个文档作为演示
      const limitedDocs = documents.slice(0, 20);
      console.log(`📝 将处理前 ${limitedDocs.length} 个文档`);
      
      // 逐个处理文档，添加延迟
      let processedCount = 0;
      for (const doc of limitedDocs) {
        try {
          await ragService.processAndStoreDocument(doc);
          processedCount++;
          
          if (processedCount % 5 === 0) {
            console.log(`✓ 已处理 ${processedCount}/${limitedDocs.length} 个文档`);
            // 添加小延迟避免API限流
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        } catch (docError) {
          console.warn(`⚠️ 跳过文档 ${doc.path}: ${docError.message}`);
          continue;
        }
      }
      
      ragService.knowledgeBaseBuilt = true;
      console.log(`✅ 成功处理 ${processedCount} 个文档`);
      return;
      
    } catch (error) {
      console.error(`❌ 第 ${attempt} 次构建失败:`, error.message);
      
      if (attempt < maxRetries) {
        console.log(`⏳ ${5}秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, 5000));
      } else {
        throw new Error(`知识库构建失败，已重试 ${maxRetries} 次`);
      }
    }
  }
}

/**
 * 测试问答功能
 */
async function testQAFunctionality(ragService) {
  const testQuestions = [
    '这个系统主要有哪些功能？',
    '如何使用地图分析工具？'
  ];
  
  for (const question of testQuestions) {
    console.log(`\n❓ 测试问题: ${question}`);
    try {
      const result = await ragService.askQuestion(question);
      const preview = result.answer.length > 150 
        ? result.answer.substring(0, 150) + '...' 
        : result.answer;
      console.log(`💬 回答预览: ${preview}`);
      console.log(`📚 来源数量: ${result.sources?.length || 0}`);
    } catch (error) {
      console.error(`❌ 问答测试失败: ${error.message}`);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initializeAIAssistant();
}

module.exports = { initializeAIAssistant }; 