/* Main CSS File - 主样式文件 */

/* 基础重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Roboto', 'Noto Sans SC', sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

#root {
  height: 100%;
  width: 100%;
}

/* 全局容器样式 */
.app-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 基础链接样式 */
a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

/* 基础按钮样式 */
button {
  cursor: pointer;
  border: none;
  outline: none;
  background: none;
  font-family: inherit;
  transition: all 0.3s ease;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 基础输入框样式 */
input, textarea {
  border: none;
  outline: none;
  font-family: inherit;
  background: transparent;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
}

/* 清除浮动 */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.d-flex {
  display: flex !important;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  html, body {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  html, body {
    font-size: 11px;
  }
} 