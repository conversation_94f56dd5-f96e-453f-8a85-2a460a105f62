/* AI 智能助手样式 - 液体玻璃设计 */

/* 浮动助手按钮 */
.ai-assistant-fab {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    rgba(76, 200, 255, 0.2) 0%, 
    rgba(76, 200, 255, 0.1) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(76, 200, 255, 0.3);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  box-shadow: 
    0 8px 32px rgba(76, 200, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.ai-assistant-fab:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 12px 40px rgba(76, 200, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.fab-icon {
  font-size: 24px;
  color: #4dc8ff;
  text-shadow: 0 0 10px rgba(76, 200, 255, 0.5);
  transition: all 0.3s ease;
}

.fab-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent, rgba(76, 200, 255, 0.3), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ai-assistant-fab:hover .fab-glow {
  opacity: 1;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 助手面板 */
.ai-assistant-panel {
  position: fixed;
  bottom: 100px;
  right: 30px;
  width: 400px;
  height: 600px;
  background: linear-gradient(135deg, 
    rgba(26, 26, 46, 0.95) 0%, 
    rgba(22, 33, 62, 0.95) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(76, 200, 255, 0.2);
  border-radius: 20px;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 999;
}

/* 面板头部 */
.assistant-header {
  padding: 20px;
  border-bottom: 1px solid rgba(76, 200, 255, 0.1);
  background: linear-gradient(135deg, 
    rgba(76, 200, 255, 0.1) 0%, 
    rgba(76, 200, 255, 0.05) 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.assistant-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4dc8ff, #00a8ff);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 15px rgba(76, 200, 255, 0.3);
}

.header-text h3 {
  margin: 0;
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
}

.header-text p {
  margin: 0;
  font-size: 12px;
  color: #b0b0b0;
}

.status.online {
  color: #2ed573;
}

.status.offline {
  color: #ff4757;
}

.doc-count {
  font-size: 10px;
  opacity: 0.8;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.build-btn, .clear-btn, .close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.build-btn {
  background: rgba(76, 200, 255, 0.2);
}

.build-btn:hover:not(:disabled) {
  background: rgba(76, 200, 255, 0.3);
  transform: scale(1.1);
}

.build-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.building-indicator {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4dc8ff;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.clear-btn:hover, .close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* 消息区域 */
.messages-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(76, 200, 255, 0.3);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(76, 200, 255, 0.5);
}

/* 消息样式 */
.message {
  max-width: 85%;
  word-wrap: break-word;
}

.message.user {
  align-self: flex-end;
}

.message.assistant {
  align-self: flex-start;
}

.message-content {
  padding: 12px 16px;
  border-radius: 16px;
  font-size: 14px;
  line-height: 1.5;
  position: relative;
}

.message.user .message-content {
  background: linear-gradient(135deg, #4dc8ff, #00a8ff);
  color: #ffffff;
  border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(76, 200, 255, 0.2);
  border-bottom-left-radius: 4px;
}

.typing-indicator {
  animation: blink 1s infinite;
  color: #4dc8ff;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.message-sources {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(76, 200, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(76, 200, 255, 0.2);
}

.message-sources small {
  color: #b0b0b0;
  font-size: 12px;
}

/* 建议问题 */
.suggestions-container {
  margin-top: 20px;
}

.suggestions-container h4 {
  color: #4dc8ff;
  font-size: 14px;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.suggestion-category {
  margin-bottom: 16px;
}

.suggestion-category h5 {
  color: #ffffff;
  font-size: 12px;
  margin: 0 0 8px 0;
  font-weight: 500;
  opacity: 0.8;
}

.suggestion-btn {
  display: block;
  width: 100%;
  padding: 10px 12px;
  margin-bottom: 6px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(76, 200, 255, 0.2);
  border-radius: 8px;
  color: #ffffff;
  font-size: 13px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-btn:hover {
  background: rgba(76, 200, 255, 0.1);
  border-color: rgba(76, 200, 255, 0.4);
  transform: translateX(4px);
}

/* 输入区域 */
.input-container {
  padding: 20px;
  border-top: 1px solid rgba(76, 200, 255, 0.1);
  background: linear-gradient(135deg, 
    rgba(76, 200, 255, 0.05) 0%, 
    rgba(76, 200, 255, 0.02) 100%);
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.input-wrapper textarea {
  flex: 1;
  min-height: 40px;
  max-height: 120px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(76, 200, 255, 0.2);
  border-radius: 12px;
  color: #ffffff;
  font-size: 14px;
  font-family: inherit;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
}

.input-wrapper textarea::placeholder {
  color: #b0b0b0;
}

.input-wrapper textarea:focus {
  border-color: rgba(76, 200, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(76, 200, 255, 0.1);
}

.input-wrapper textarea:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #4dc8ff, #00a8ff);
  color: #ffffff;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 4px 15px rgba(76, 200, 255, 0.3);
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 200, 255, 0.4);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-assistant-panel {
    width: calc(100vw - 40px);
    height: calc(100vh - 140px);
    right: 20px;
    bottom: 100px;
  }
  
  .ai-assistant-fab {
    bottom: 20px;
    right: 20px;
  }
}

/* 暗色模式适配 */
@media (prefers-color-scheme: dark) {
  .ai-assistant-panel {
    background: linear-gradient(135deg, 
      rgba(16, 16, 32, 0.95) 0%, 
      rgba(12, 23, 42, 0.95) 100%);
  }
  
  .assistant-header {
    background: linear-gradient(135deg, 
      rgba(76, 200, 255, 0.08) 0%, 
      rgba(76, 200, 255, 0.03) 100%);
  }
}
