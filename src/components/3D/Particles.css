/* Particles Component Styles - 粒子组件样式 */

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.particles-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
}

/* 粒子系统优化 */
.particles-container canvas {
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .particles-container {
    /* 在移动设备上减少粒子密度以提高性能 */
    opacity: 0.7;
  }
}

@media (max-width: 480px) {
  .particles-container {
    opacity: 0.5;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .particles-container {
    filter: brightness(0.8);
  }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .particles-container {
    opacity: 0.3;
  }
  
  .particles-container canvas {
    animation: none !important;
  }
} 