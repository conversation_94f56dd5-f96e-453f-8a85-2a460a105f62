# 🤖 Industrial Geo Dev AI 智能助手

## 概述

Industrial Geo Dev AI 智能助手是一个基于 RAG (Retrieval-Augmented Generation) 技术的高度智能化助手系统。它作为嵌入在系统内部的项目专家，能够实时理解并回答用户的提问，为用户在使用复杂的地图分析和数据可视化功能时提供全程引导和帮助。

## 🌟 核心特性

### 💡 智能问答
- **实时流式响应**: 使用 Gemini 1.5 Flash 模型提供快速、准确的答案
- **上下文理解**: 基于项目代码和文档的深度知识库
- **专业指导**: 针对地理信息系统、数据分析和工业选址的专业建议

### 🧠 RAG 知识库
- **自动索引**: 自动扫描和索引项目中的代码、文档和配置文件
- **智能检索**: 使用向量相似度搜索最相关的信息片段
- **动态更新**: 支持知识库的增量更新和重建

### 🎯 智能建议
- **个性化建议**: 基于项目功能的智能建议问题
- **分类组织**: 按功能、技术支持、数据分析等维度组织
- **动态调整**: 根据用户交互历史动态调整建议内容

## 🚀 快速开始

### 1. 环境准备

确保您的系统满足以下要求：
- Node.js >= 18.0.0
- npm 或 yarn 包管理器
- 已配置 Gemini API 密钥

### 2. 初始化 AI 助手

使用提供的管理脚本快速初始化：

```bash
# 初始化 AI 助手并构建知识库
node manage-ai-assistant.js init

# 检查 AI 助手状态
node manage-ai-assistant.js status

# 测试 AI 助手功能
node manage-ai-assistant.js test
```

### 3. 启动服务

```bash
# 启动前端开发服务器
npm run dev

# 启动后端服务器
cd backend-server
npm run dev
```

## 🔧 配置说明

### API 密钥配置

AI 助手使用 Google Gemini API，需要配置 API 密钥：

1. 在 `backend-server` 目录创建 `.env` 文件
2. 添加以下配置：

```env
GEMINI_API_KEY=你的_Gemini_API_密钥
NODE_ENV=development
PORT=3001
```

### 模型选择

系统默认使用经济实惠的 `gemini-1.5-flash` 模型：
- **生成模型**: `gemini-1.5-flash` - 平衡性能和成本
- **嵌入模型**: `text-embedding-004` - 高质量向量嵌入

## 📚 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 UI       │    │   后端 API      │    │   Gemini AI     │
│                 │    │                 │    │                 │
│ AIAssistant.jsx │───▶│ /api/rag/*      │───▶│ gemini-1.5-flash│
│ ragApi.js       │    │ ragService.js   │    │ text-embedding  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         │                       ▼
         │              ┌─────────────────┐
         │              │   知识库        │
         │              │                 │
         └──────────────│ 代码 + 文档     │
                        │ 向量存储        │
                        └─────────────────┘
```

## 🎯 使用场景

### 1. 系统功能指导
```
用户: "如何使用地图分析工具？"
AI: "我来为您详细介绍地图分析工具的使用方法..."
```

### 2. 技术问题解答
```
用户: "地图加载很慢怎么办？"
AI: "遇到地图加载缓慢的问题，可以尝试以下解决方案..."
```

### 3. 数据分析指导
```
用户: "如何分析QCEW工资数据？"
AI: "QCEW工资数据分析可以通过以下步骤进行..."
```

## 🛠️ 高级功能

### 知识库管理

```bash
# 重新构建知识库
node manage-ai-assistant.js rebuild

# 查看知识库状态
curl http://localhost:3001/api/rag/status
```

### 流式问答

AI 助手支持实时流式响应，提供更好的用户体验：

```javascript
// 前端调用示例
ragApiService.askQuestionStream(
  question,
  (data) => {
    // 处理流式数据
    if (data.type === 'content') {
      updateUI(data.content);
    }
  },
  (error) => console.error(error),
  () => console.log('完成')
);
```

### 自定义建议问题

可以通过修改 `backend-server/routes/rag.js` 中的建议问题来自定义：

```javascript
const suggestions = [
  {
    category: "自定义分类",
    questions: [
      "你的自定义问题1",
      "你的自定义问题2"
    ]
  }
];
```

## 🔍 故障排除

### 常见问题

1. **AI 助手无响应**
   ```bash
   # 检查服务状态
   node manage-ai-assistant.js status
   
   # 重新初始化
   node manage-ai-assistant.js init
   ```

2. **知识库为空**
   ```bash
   # 重建知识库
   node manage-ai-assistant.js rebuild
   ```

3. **API 密钥错误**
   - 检查 `.env` 文件中的 `GEMINI_API_KEY` 配置
   - 确认 API 密钥有效且有足够配额

### 调试模式

启用调试模式查看详细日志：

```bash
DEBUG=* node backend-server/server.cjs
```

## 📈 性能优化

### 1. 知识库优化
- 定期清理无用的文档片段
- 优化分块策略以提高检索准确性
- 使用缓存减少重复的嵌入计算

### 2. 响应优化
- 启用流式响应减少感知延迟
- 实现智能缓存常见问题的答案
- 使用连接池优化 API 调用

## 🔒 安全考虑

1. **API 密钥安全**
   - 绝不在前端代码中暴露 API 密钥
   - 使用环境变量管理敏感信息
   - 定期轮换 API 密钥

2. **输入验证**
   - 对用户输入进行严格验证
   - 限制问题长度和频率
   - 防止注入攻击

3. **数据隐私**
   - 不记录敏感的用户问题
   - 遵循数据保护法规
   - 提供用户数据删除选项

## 📞 支持与贡献

如果您遇到问题或有改进建议，请：

1. 查看本文档的故障排除部分
2. 检查系统日志获取详细错误信息
3. 提交 issue 或 pull request

## 📄 许可证

本项目遵循 MIT 许可证。

---

**🎉 享受您的 AI 助手体验！**

AI 助手将持续学习和改进，为您提供越来越好的服务体验。 