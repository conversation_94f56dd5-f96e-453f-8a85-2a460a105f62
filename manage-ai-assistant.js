#!/usr/bin/env node

/**
 * Industrial Geo Dev AI 助手管理脚本
 * 提供 AI 助手相关的管理功能
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const BACKEND_DIR = path.join(__dirname, 'backend-server');

function printBanner() {
  console.log('\n🤖 ========================================');
  console.log('   Industrial Geo Dev AI Assistant');
  console.log('   智能助手管理工具');
  console.log('========================================\n');
}

function printHelp() {
  console.log('可用命令:');
  console.log('  init      - 初始化 AI 助手并构建知识库');
  console.log('  status    - 检查 AI 助手状态');
  console.log('  rebuild   - 重新构建知识库');
  console.log('  test      - 测试 AI 助手功能');
  console.log('  help      - 显示帮助信息');
  console.log('\n使用示例:');
  console.log('  node manage-ai-assistant.js init');
  console.log('  node manage-ai-assistant.js status');
  console.log('');
}

function runBackendScript(scriptName, args = []) {
  return new Promise((resolve, reject) => {
    const scriptPath = path.join(BACKEND_DIR, scriptName);
    
    if (!fs.existsSync(scriptPath)) {
      reject(new Error(`脚本文件不存在: ${scriptPath}`));
      return;
    }

    const child = spawn('node', [scriptPath, ...args], {
      cwd: BACKEND_DIR,
      stdio: 'inherit'
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`脚本执行失败，退出码: ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function checkEnvironment() {
  console.log('🔍 检查运行环境...');
  
  // 检查 Node.js 版本
  const nodeVersion = process.version;
  console.log(`Node.js 版本: ${nodeVersion}`);
  
  // 检查后端目录
  if (!fs.existsSync(BACKEND_DIR)) {
    throw new Error('后端目录不存在');
  }
  
  // 检查 package.json
  const packagePath = path.join(BACKEND_DIR, 'package.json');
  if (!fs.existsSync(packagePath)) {
    throw new Error('后端 package.json 不存在');
  }
  
  // 检查依赖是否安装
  const nodeModulesPath = path.join(BACKEND_DIR, 'node_modules');
  if (!fs.existsSync(nodeModulesPath)) {
    console.log('⚠️  检测到依赖未安装，正在安装...');
    await new Promise((resolve, reject) => {
      const npm = spawn('npm', ['install'], {
        cwd: BACKEND_DIR,
        stdio: 'inherit'
      });
      
      npm.on('close', (code) => {
        if (code === 0) {
          console.log('✅ 依赖安装完成');
          resolve();
        } else {
          reject(new Error(`依赖安装失败，退出码: ${code}`));
        }
      });
    });
  }
  
  console.log('✅ 环境检查通过\n');
}

async function initAIAssistant() {
  console.log('🚀 初始化 AI 助手...\n');
  
  try {
    await checkEnvironment();
    await runBackendScript('init-ai-assistant.js');
    
    console.log('\n🎉 AI 助手初始化完成！');
    console.log('💡 现在您可以启动服务器并使用 AI 助手功能了。');
    console.log('');
    console.log('启动命令:');
    console.log('  前端: npm run dev');
    console.log('  后端: cd backend-server && npm run dev');
    
  } catch (error) {
    console.error('❌ 初始化失败:', error.message);
    process.exit(1);
  }
}

async function checkAIStatus() {
  console.log('📊 检查 AI 助手状态...\n');
  
  try {
    await runBackendScript('rag-status');
  } catch (error) {
    console.error('❌ 状态检查失败:', error.message);
  }
}

async function rebuildKnowledge() {
  console.log('🔨 重新构建知识库...\n');
  
  try {
    await runBackendScript('build-knowledge');
    console.log('\n✅ 知识库重建完成！');
  } catch (error) {
    console.error('❌ 知识库重建失败:', error.message);
  }
}

async function testAIAssistant() {
  console.log('🧪 测试 AI 助手功能...\n');
  
  try {
    // 运行基本的功能测试
    const testScript = `
      const RAGService = require('./services/ragService');
      async function test() {
        const rag = new RAGService();
        await rag.initialize();
        const result = await rag.askQuestion('系统有哪些主要功能？');
        console.log('测试结果:', result.answer.substring(0, 100) + '...');
      }
      test().catch(console.error);
    `;
    
    const testFile = path.join(BACKEND_DIR, 'temp-test.js');
    fs.writeFileSync(testFile, testScript);
    
    await runBackendScript('temp-test.js');
    
    // 清理临时文件
    fs.unlinkSync(testFile);
    
    console.log('\n✅ AI 助手测试完成！');
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

async function main() {
  printBanner();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'init':
      await initAIAssistant();
      break;
      
    case 'status':
      await checkAIStatus();
      break;
      
    case 'rebuild':
      await rebuildKnowledge();
      break;
      
    case 'test':
      await testAIAssistant();
      break;
      
    case 'help':
    case undefined:
      printHelp();
      break;
      
    default:
      console.log(`❌ 未知命令: ${command}`);
      printHelp();
      process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 执行失败:', error.message);
    process.exit(1);
  });
} 